import React, { useState, useCallback, useEffect } from 'react';
import {
  Mic,
  Upload,
  Volume2,
  Download,
  RefreshCw,
  Settings,
  Wand2,
  CheckCircle,
  XCircle,
  Loader2,
  Music,
  FileAudio
} from 'lucide-react';
import { invoke } from '@tauri-apps/api/core';
import { open } from '@tauri-apps/plugin-dialog';
import { useNotifications } from '../../components/NotificationSystem';
import {
  AudioUploadRequest,
  AudioUploadResponse,
  VoiceCloneRequest,
  VoiceCloneResponse,
  VoiceInfo,
  GetVoicesResponse,
  SpeechGenerationRequest,
  SpeechGenerationResponse,
  AudioUploadStatus,
  VoiceCloneStatus,
  SpeechGenerationStatus,
  AudioFileInfo,
  AudioUploadState,
  VoiceCloneState,
  SpeechGenerationState
} from '../../types/voiceClone';

/**
 * 声音克隆与TTS工具
 * 遵循 Tauri 开发规范和 UI/UX 设计标准
 */
const VoiceCloneTool: React.FC = () => {
  const { addNotification } = useNotifications();

  // ============= 状态管理 =============
  
  // 音频上传状态
  const [audioFile, setAudioFile] = useState<AudioFileInfo | null>(null);
  const [uploadState, setUploadState] = useState<AudioUploadState>({
    status: AudioUploadStatus.IDLE,
    progress: 0
  });

  // 声音克隆状态
  const [cloneText, setCloneText] = useState('');
  const [customVoiceId, setCustomVoiceId] = useState('');
  const [cloneState, setCloneState] = useState<VoiceCloneState>({
    status: VoiceCloneStatus.IDLE
  });

  // 音色管理状态
  const [voices, setVoices] = useState<VoiceInfo[]>([]);
  const [selectedVoiceId, setSelectedVoiceId] = useState<string>('');
  const [isLoadingVoices, setIsLoadingVoices] = useState(false);

  // 语音生成状态
  const [speechRequest, setSpeechRequest] = useState<SpeechGenerationRequest>({
    text: '',
    voice_id: '',
    speed: 1.0,
    vol: 1.0,
    emotion: 'calm'
  });
  const [speechState, setSpeechState] = useState<SpeechGenerationState>({
    status: SpeechGenerationStatus.IDLE
  });



  // ============= 音频上传功能 =============

  const handleFileSelect = useCallback(async () => {
    try {
      const selected = await open({
        multiple: false,
        filters: [{
          name: 'Audio Files',
          extensions: ['wav', 'mp3', 'flac', 'm4a', 'aac', 'ogg']
        }]
      });

      if (selected && typeof selected === 'string') {
        const file = new File([], selected.split('/').pop() || 'audio');
        const audioInfo: AudioFileInfo = {
          file,
          name: file.name,
          size: 0, // 实际应该获取文件大小
          type: file.type || 'audio/wav',
          preview_url: selected
        };
        
        setAudioFile(audioInfo);
        addNotification({
          type: 'success',
          title: '文件选择成功',
          message: `已选择音频文件: ${audioInfo.name}`
        });
      }
    } catch (error) {
      console.error('文件选择失败:', error);
      addNotification({
        type: 'error',
        title: '文件选择失败',
        message: `选择文件时出错: ${error}`
      });
    }
  }, [addNotification]);

  const handleUploadAudio = useCallback(async () => {
    if (!audioFile?.preview_url) {
      addNotification({
        type: 'warning',
        title: '请先选择音频文件',
        message: '请选择要上传的音频文件'
      });
      return;
    }

    setUploadState({
      status: AudioUploadStatus.UPLOADING,
      progress: 0
    });

    try {
      const request: AudioUploadRequest = {
        audio_file_path: audioFile.preview_url,
        purpose: 'voice_clone'
      };

      const response = await invoke<AudioUploadResponse>('upload_audio_file', { request });

      if (response.status) {
        setUploadState({
          status: AudioUploadStatus.SUCCESS,
          progress: 100,
          result: response
        });
        
        addNotification({
          type: 'success',
          title: '音频上传成功',
          message: response.msg || '音频文件已成功上传到云端'
        });
      } else {
        throw new Error(response.msg || '上传失败');
      }
    } catch (error) {
      console.error('音频上传失败:', error);
      setUploadState({
        status: AudioUploadStatus.ERROR,
        progress: 0,
        error: String(error)
      });
      
      addNotification({
        type: 'error',
        title: '音频上传失败',
        message: `上传失败: ${error}`
      });
    }
  }, [audioFile, addNotification]);

  // ============= 声音克隆功能 =============

  const handleVoiceClone = useCallback(async () => {
    if (!cloneText.trim()) {
      addNotification({
        type: 'warning',
        title: '请输入克隆文本',
        message: '请输入用于声音克隆的文本内容'
      });
      return;
    }

    if (!audioFile?.preview_url) {
      addNotification({
        type: 'warning',
        title: '请先上传音频文件',
        message: '请先上传参考音频文件'
      });
      return;
    }

    setCloneState({
      status: VoiceCloneStatus.PROCESSING,
      progress: '正在处理声音克隆...'
    });

    try {
      // 生成唯一的 voice_id
      const finalVoiceId = customVoiceId.trim() || `voice_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      const request: VoiceCloneRequest = {
        text: cloneText,
        model: 'speech-02-hd',
        need_noise_reduction: true,
        voice_id: finalVoiceId,
        prefix: 'BoWong-',
        audio_file_path: audioFile.preview_url
      };

      const response = await invoke<VoiceCloneResponse>('clone_voice', { request });

      if (response.status && response.data) {
        setCloneState({
          status: VoiceCloneStatus.SUCCESS,
          result: response
        });

        const voiceId = response.extra?.voice_id || '未知';
        addNotification({
          type: 'success',
          title: '声音克隆成功',
          message: `新音色ID: ${voiceId}`
        });

        // 刷新音色列表
        await loadVoices();
      } else {
        throw new Error(response.msg || '克隆失败');
      }
    } catch (error) {
      console.error('声音克隆失败:', error);
      setCloneState({
        status: VoiceCloneStatus.ERROR,
        error: String(error)
      });
      
      addNotification({
        type: 'error',
        title: '声音克隆失败',
        message: `克隆失败: ${error}`
      });
    }
  }, [cloneText, audioFile, addNotification]);

  // ============= 音色管理功能 =============

  const loadVoices = useCallback(async () => {
    setIsLoadingVoices(true);
    try {
      const response = await invoke<GetVoicesResponse>('get_voices');
      
      if (response.status && response.data) {
        setVoices(response.data);
        
        // 如果没有选中的音色且有可用音色，选择第一个
        if (!selectedVoiceId && response.data && response.data.length > 0) {
          const firstVoice = response.data[0];
          setSelectedVoiceId(firstVoice.voice_id);
          setSpeechRequest(prev => ({
            ...prev,
            voice_id: firstVoice.voice_id
          }));
        }
      }
    } catch (error) {
      console.error('获取音色列表失败:', error);
      addNotification({
        type: 'error',
        title: '获取音色列表失败',
        message: `获取失败: ${error}`
      });
    } finally {
      setIsLoadingVoices(false);
    }
  }, [selectedVoiceId, addNotification]);

  const handleVoiceSelect = useCallback((voiceId: string) => {
    setSelectedVoiceId(voiceId);
    setSpeechRequest(prev => ({
      ...prev,
      voice_id: voiceId
    }));
  }, []);

  // ============= 语音生成功能 =============

  const handleGenerateSpeech = useCallback(async () => {
    if (!speechRequest.text.trim()) {
      addNotification({
        type: 'warning',
        title: '请输入要合成的文本',
        message: '请输入要转换为语音的文本内容'
      });
      return;
    }

    if (!speechRequest.voice_id) {
      addNotification({
        type: 'warning',
        title: '请选择音色',
        message: '请选择要使用的音色'
      });
      return;
    }

    setSpeechState({
      status: SpeechGenerationStatus.GENERATING,
      progress: '正在生成语音...'
    });

    try {
      const response = await invoke<SpeechGenerationResponse>('generate_speech', { 
        request: speechRequest 
      });

      if (response.status && response.data) {
        setSpeechState({
          status: SpeechGenerationStatus.SUCCESS,
          result: response
        });
        
        addNotification({
          type: 'success',
          title: '语音生成成功',
          message: '语音已成功生成，可以播放或下载'
        });
      } else {
        throw new Error(response.msg || '生成失败');
      }
    } catch (error) {
      console.error('语音生成失败:', error);
      setSpeechState({
        status: SpeechGenerationStatus.ERROR,
        error: String(error)
      });
      
      addNotification({
        type: 'error',
        title: '语音生成失败',
        message: `生成失败: ${error}`
      });
    }
  }, [speechRequest, addNotification]);

  // ============= 初始化 =============

  useEffect(() => {
    loadVoices();
  }, [loadVoices]);

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="page-header flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300">
            <Mic className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-purple-600 bg-clip-text text-transparent">
              声音克隆与TTS工具
            </h1>
            <p className="text-gray-600 text-lg">专业的语音合成和声音克隆功能</p>
          </div>
        </div>

        <button
          onClick={loadVoices}
          disabled={isLoadingVoices}
          className="flex items-center gap-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <RefreshCw className={`w-4 h-4 ${isLoadingVoices ? 'animate-spin' : ''}`} />
          刷新音色
        </button>
      </div>

      {/* 主要内容区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 左侧：音频上传和声音克隆 */}
        <div className="space-y-6">
          {/* 音频上传卡片 */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-3 mb-4">
              <Upload className="w-5 h-5 text-blue-600" />
              <h3 className="text-lg font-semibold text-gray-900">音频上传</h3>
            </div>

            <div className="space-y-4">
              {/* 文件选择 */}
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors">
                {audioFile ? (
                  <div className="flex items-center justify-center gap-3">
                    <FileAudio className="w-8 h-8 text-blue-600" />
                    <div>
                      <p className="font-medium text-gray-900">{audioFile.name}</p>
                      <p className="text-sm text-gray-500">
                        {audioFile.type} • {(audioFile.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                  </div>
                ) : (
                  <div>
                    <Music className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                    <p className="text-gray-600 mb-2">点击选择音频文件</p>
                    <p className="text-sm text-gray-500">支持 WAV, MP3, FLAC, M4A, AAC, OGG 格式</p>
                  </div>
                )}
                
                <button
                  onClick={handleFileSelect}
                  className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  选择文件
                </button>
              </div>

              {/* 上传按钮和状态 */}
              {audioFile && (
                <div className="space-y-3">
                  <button
                    onClick={handleUploadAudio}
                    disabled={uploadState.status === AudioUploadStatus.UPLOADING}
                    className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {uploadState.status === AudioUploadStatus.UPLOADING ? (
                      <>
                        <Loader2 className="w-4 h-4 animate-spin" />
                        上传中...
                      </>
                    ) : (
                      <>
                        <Upload className="w-4 h-4" />
                        上传到云端
                      </>
                    )}
                  </button>

                  {/* 上传状态显示 */}
                  {uploadState.status !== AudioUploadStatus.IDLE && (
                    <div className="flex items-center gap-2 text-sm">
                      {uploadState.status === AudioUploadStatus.SUCCESS && (
                        <>
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          <span className="text-green-600">上传成功</span>
                        </>
                      )}
                      {uploadState.status === AudioUploadStatus.ERROR && (
                        <>
                          <XCircle className="w-4 h-4 text-red-600" />
                          <span className="text-red-600">上传失败: {uploadState.error}</span>
                        </>
                      )}
                      {uploadState.status === AudioUploadStatus.UPLOADING && (
                        <>
                          <Loader2 className="w-4 h-4 animate-spin text-blue-600" />
                          <span className="text-blue-600">上传进度: {uploadState.progress}%</span>
                        </>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* 声音克隆卡片 */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-3 mb-4">
              <Wand2 className="w-5 h-5 text-purple-600" />
              <h3 className="text-lg font-semibold text-gray-900">声音克隆</h3>
            </div>

            <div className="space-y-4">
              {/* 克隆文本输入 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  克隆文本
                </label>
                <textarea
                  value={cloneText}
                  onChange={(e) => setCloneText(e.target.value)}
                  placeholder="请输入用于声音克隆的文本内容..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
                  rows={3}
                />
              </div>

              {/* 自定义音色ID输入 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  自定义音色ID
                  <span className="text-xs text-gray-500 ml-2">(可选，留空将自动生成唯一ID)</span>
                </label>
                <input
                  type="text"
                  value={customVoiceId}
                  onChange={(e) => setCustomVoiceId(e.target.value)}
                  placeholder="例如: my_voice_001 (留空自动生成)"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
                <p className="text-xs text-gray-500 mt-1">
                  音色ID用于标识您的声音模型。留空时将自动生成唯一ID避免重复
                </p>
              </div>

              {/* 克隆按钮 */}
              <button
                onClick={handleVoiceClone}
                disabled={cloneState.status === VoiceCloneStatus.PROCESSING}
                className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {cloneState.status === VoiceCloneStatus.PROCESSING ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin" />
                    克隆中...
                  </>
                ) : (
                  <>
                    <Wand2 className="w-4 h-4" />
                    开始克隆
                  </>
                )}
              </button>

              {/* 克隆状态显示 */}
              {cloneState.status !== VoiceCloneStatus.IDLE && (
                <div className="flex items-center gap-2 text-sm">
                  {cloneState.status === VoiceCloneStatus.SUCCESS && (
                    <>
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      <span className="text-green-600">克隆成功</span>
                    </>
                  )}
                  {cloneState.status === VoiceCloneStatus.ERROR && (
                    <>
                      <XCircle className="w-4 h-4 text-red-600" />
                      <span className="text-red-600">克隆失败: {cloneState.error}</span>
                    </>
                  )}
                  {cloneState.status === VoiceCloneStatus.PROCESSING && (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin text-purple-600" />
                      <span className="text-purple-600">{cloneState.progress}</span>
                    </>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 右侧：音色管理和语音合成 */}
        <div className="space-y-6">
          {/* 音色管理卡片 */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <Volume2 className="w-5 h-5 text-orange-600" />
                <h3 className="text-lg font-semibold text-gray-900">音色管理</h3>
              </div>
              <span className="text-sm text-gray-500">{voices.length} 个音色</span>
            </div>

            <div className="space-y-3 max-h-64 overflow-y-auto">
              {isLoadingVoices ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="w-6 h-6 animate-spin text-gray-400" />
                  <span className="ml-2 text-gray-500">加载中...</span>
                </div>
              ) : voices.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Volume2 className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                  <p>暂无可用音色</p>
                  <p className="text-sm">请先克隆一个音色</p>
                </div>
              ) : (
                voices.map((voice) => (
                  <div
                    key={voice.voice_id}
                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                      selectedVoiceId === voice.voice_id
                        ? 'border-orange-500 bg-orange-50'
                        : 'border-gray-200 hover:border-orange-300'
                    }`}
                    onClick={() => handleVoiceSelect(voice.voice_id)}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-gray-900">
                          {voice.voice_name || voice.voice_id}
                        </p>
                        {voice.description && voice.description.length > 0 && (
                          <p className="text-sm text-gray-500">{voice.description.join(', ')}</p>
                        )}
                        {voice.created_time && (
                          <p className="text-xs text-gray-400">创建时间: {voice.created_time}</p>
                        )}
                      </div>
                      <div className="flex items-center gap-2">
                        {selectedVoiceId === voice.voice_id && (
                          <CheckCircle className="w-4 h-4 text-orange-600" />
                        )}
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* 语音合成卡片 */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-3 mb-4">
              <Settings className="w-5 h-5 text-green-600" />
              <h3 className="text-lg font-semibold text-gray-900">语音合成</h3>
            </div>

            <div className="space-y-4">
              {/* 合成文本输入 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  合成文本
                </label>
                <textarea
                  value={speechRequest.text}
                  onChange={(e) => setSpeechRequest(prev => ({ ...prev, text: e.target.value }))}
                  placeholder="请输入要转换为语音的文本..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none"
                  rows={3}
                />
              </div>

              {/* 参数控制 */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    语速: {speechRequest.speed}
                  </label>
                  <input
                    type="range"
                    min="0.5"
                    max="2"
                    step="0.1"
                    value={speechRequest.speed}
                    onChange={(e) => setSpeechRequest(prev => ({ 
                      ...prev, 
                      speed: parseFloat(e.target.value) 
                    }))}
                    className="w-full"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    音量: {speechRequest.vol}
                  </label>
                  <input
                    type="range"
                    min="0.1"
                    max="2"
                    step="0.1"
                    value={speechRequest.vol}
                    onChange={(e) => setSpeechRequest(prev => ({ 
                      ...prev, 
                      vol: parseFloat(e.target.value) 
                    }))}
                    className="w-full"
                  />
                </div>
              </div>

              {/* 情感选择 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  情感
                </label>
                <select
                  value={speechRequest.emotion}
                  onChange={(e) => setSpeechRequest(prev => ({ 
                    ...prev, 
                    emotion: e.target.value as any 
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  <option value="calm">平静</option>
                  <option value="happy">快乐</option>
                  <option value="sad">悲伤</option>
                  <option value="angry">愤怒</option>
                  <option value="fearful">恐惧</option>
                  <option value="disgusted">厌恶</option>
                  <option value="surprised">惊讶</option>
                </select>
              </div>

              {/* 生成按钮 */}
              <button
                onClick={handleGenerateSpeech}
                disabled={speechState.status === SpeechGenerationStatus.GENERATING}
                className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {speechState.status === SpeechGenerationStatus.GENERATING ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin" />
                    生成中...
                  </>
                ) : (
                  <>
                    <Volume2 className="w-4 h-4" />
                    生成语音
                  </>
                )}
              </button>

              {/* 生成状态显示 */}
              {speechState.status !== SpeechGenerationStatus.IDLE && (
                <div className="space-y-3">
                  <div className="flex items-center gap-2 text-sm">
                    {speechState.status === SpeechGenerationStatus.SUCCESS && (
                      <>
                        <CheckCircle className="w-4 h-4 text-green-600" />
                        <span className="text-green-600">生成成功</span>
                      </>
                    )}
                    {speechState.status === SpeechGenerationStatus.ERROR && (
                      <>
                        <XCircle className="w-4 h-4 text-red-600" />
                        <span className="text-red-600">生成失败: {speechState.error}</span>
                      </>
                    )}
                    {speechState.status === SpeechGenerationStatus.GENERATING && (
                      <>
                        <Loader2 className="w-4 h-4 animate-spin text-green-600" />
                        <span className="text-green-600">{speechState.progress}</span>
                      </>
                    )}
                  </div>

                  {/* 音频播放器 */}
                  {speechState.result?.data?.audio_url && (
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center justify-between mb-3">
                        <span className="text-sm font-medium text-gray-700">生成的语音</span>
                        <button
                          onClick={() => {
                            // 下载音频文件
                            const link = document.createElement('a');
                            link.href = speechState.result!.data!.audio_url;
                            link.download = 'generated_speech.wav';
                            link.click();
                          }}
                          className="flex items-center gap-1 px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                        >
                          <Download className="w-3 h-3" />
                          下载
                        </button>
                      </div>
                      
                      <audio
                        controls
                        src={speechState.result.data.audio_url}
                        className="w-full"
                      >
                        您的浏览器不支持音频播放
                      </audio>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VoiceCloneTool;
